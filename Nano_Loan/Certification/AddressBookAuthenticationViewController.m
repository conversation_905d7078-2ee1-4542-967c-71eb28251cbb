//
//  AddressBookAuthenticationViewController.m
//  Nano_Loan
//
//  需求说明：
//  • editMode 控制已完成信息是否可编辑。
//  • 需拉取紧急联系人列表 & 保存修改接口，参数需含 splendid。
//  • 本地通讯录读取需请求用户权限，展示联系人选择。
//  • 保存/修改成功后刷新状态，更新按钮颜色。
//

#import "AddressBookAuthenticationViewController.h"
#import "NetworkManager.h"
#import <Contacts/Contacts.h>
#import <ContactsUI/ContactsUI.h>
#import <MBProgressHUD/MBProgressHUD.h>
#import "HUD.h"
#import "RiskEventManager.h"
#import "OptionPickerViewController.h"
#define SCALE_375 (UIScreen.mainScreen.bounds.size.width / 375.0)

@interface AddressBookAuthenticationViewController () <UITableViewDataSource, UITableViewDelegate, CNContactPickerDelegate>

@property (nonatomic, strong) UITableView *tableView;

// 拉取到的联系人模板列表，每个元素对应一个紧急联系人（字典）
@property (nonatomic, strong) NSMutableArray<NSDictionary *> *templates;

// 用户编辑后的结果数组，与 templates 顺序一致，内部元素格式：@{ name, phone, relationKey, relationName }
@property (nonatomic, strong) NSMutableArray<NSMutableDictionary *> *outputs;

@property (nonatomic, assign) NSTimeInterval pageStartTime;
@property (nonatomic, strong) UIView *navView;            // 自定义导航
@property (nonatomic, strong) UIImageView *panelView;     // 内容背景
@property (nonatomic, strong) UIButton *nextButton;       // Next 按钮
@property (nonatomic, strong) UIScrollView *scrollView;   // 表单滚动视图
@property (nonatomic, strong) NSLayoutConstraint *panelBottomConstraint; // 背景图底部约束
@property (nonatomic, strong) UIImageView *stepLogoBg;    // 步骤logo背景图

@end

@implementation AddressBookAuthenticationViewController

+ (NSString *)ab_safeFullNameForContact:(CNContact *)contact {
    // 尝试使用 CNContactFormatter，但若缺少属性会抛异常，因此采用手动拼接避免崩溃
    NSString *given  = contact.givenName ?: @"";
    NSString *family = contact.familyName ?: @"";
    NSString *middle = contact.middleName ?: @"";

    NSMutableArray<NSString *> *parts = [NSMutableArray array];
    if (family.length) [parts addObject:family];
    if (middle.length) [parts addObject:middle];
    if (given.length)  [parts addObject:given];

    return [parts componentsJoinedByString:@" "];
}

- (void)viewDidLoad {
    [super viewDidLoad];
    // 通用背景
    [self setupBackgroundImage];
    self.view.backgroundColor = [UIColor systemBackgroundColor];

    // 记录埋点时间
    self.pageStartTime = [[NSDate date] timeIntervalSince1970];

    // 自定义导航
    [self setupCustomNavBar];

    // 构建 UI
    [self buildUI];

    // 添加点击空白处收起键盘
    UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(dismissKeyboard)];
    tap.cancelsTouchesInView = NO;
    [self.view addGestureRecognizer:tap];

    // 添加测试数据以验证TableView是否工作正常
    [self addTestDataForDebugging];

    // 拉取数据
    [self fetchContactsTemplate];
}

#pragma mark - 自定义导航
- (void)setupCustomNavBar {
    self.navView = [[UIView alloc] init];
    self.navView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.view addSubview:self.navView];
    UILayoutGuide *safe = self.view.safeAreaLayoutGuide;
    [NSLayoutConstraint activateConstraints:@[
        [self.navView.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor],
        [self.navView.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor],
        [self.navView.topAnchor constraintEqualToAnchor:safe.topAnchor],
        [self.navView.heightAnchor constraintEqualToConstant:44]
    ]];

    UIButton *back = [UIButton buttonWithType:UIButtonTypeCustom];
    [back setImage:[UIImage imageNamed:@"nav_back"] forState:UIControlStateNormal];
    back.translatesAutoresizingMaskIntoConstraints = NO;
    [back addTarget:self action:@selector(backAction) forControlEvents:UIControlEventTouchUpInside];
    [self.navView addSubview:back];
    [NSLayoutConstraint activateConstraints:@[
        [back.leadingAnchor constraintEqualToAnchor:self.navView.leadingAnchor constant:16],
        [back.centerYAnchor constraintEqualToAnchor:self.navView.centerYAnchor],
        [back.widthAnchor constraintEqualToConstant:34],
        [back.heightAnchor constraintEqualToConstant:34]
    ]];

    UILabel *titleLabel = [[UILabel alloc] init];
    titleLabel.text = @"Identity Authentication";
    titleLabel.font = [UIFont boldSystemFontOfSize:22];
    titleLabel.textColor = UIColor.whiteColor;
    titleLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [self.navView addSubview:titleLabel];
    [NSLayoutConstraint activateConstraints:@[
        [titleLabel.centerXAnchor constraintEqualToAnchor:self.navView.centerXAnchor],
        [titleLabel.centerYAnchor constraintEqualToAnchor:self.navView.centerYAnchor]
    ]];
}

- (void)backAction {
    [self.navigationController popViewControllerAnimated:YES];
}

#pragma mark - 构建 UI
- (void)buildUI {
    // 先移除旧视图（适配下拉刷新等场景）
    [self.panelView removeFromSuperview];
    [self.nextButton removeFromSuperview];
    [self.scrollView removeFromSuperview];
    [self.stepLogoBg removeFromSuperview];

    // 重置约束引用
    self.panelBottomConstraint = nil;

    // 4. Next 按钮 - 先创建，用于确定滚动区域
    UIButton *nextBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    nextBtn.translatesAutoresizingMaskIntoConstraints = NO;
    [nextBtn setBackgroundImage:[UIImage imageNamed:@"home_apply_bg"] forState:UIControlStateNormal];
    [nextBtn setTitle:@"Next" forState:UIControlStateNormal];
    [nextBtn setTitleColor:UIColor.whiteColor forState:UIControlStateNormal];
    nextBtn.titleLabel.font = [UIFont fontWithName:@"Verdana-BoldItalic" size:22.0];
    nextBtn.layer.cornerRadius = 25;
    nextBtn.clipsToBounds = YES;
    [nextBtn addTarget:self action:@selector(saveTapped) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:nextBtn];
    self.nextButton = nextBtn;

    [NSLayoutConstraint activateConstraints:@[
        [nextBtn.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor constant:14],
        [nextBtn.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor constant:-14],
        // 调整到安全区域底部，避免被其他视图遮挡并保证交互
        [nextBtn.bottomAnchor constraintEqualToAnchor:self.view.safeAreaLayoutGuide.bottomAnchor constant:-20],
        [nextBtn.heightAnchor constraintEqualToConstant:50]
    ]];

    // 1. 内容背景图 panel - 改为可拉伸高度
    UIImageView *panel = [[UIImageView alloc] initWithImage:[UIImage imageNamed:@"authentication_c_bg"]]; // 若找不到则为空占位
    panel.userInteractionEnabled = YES;
    panel.translatesAutoresizingMaskIntoConstraints = NO;
    panel.layer.cornerRadius = 16.0;
    panel.clipsToBounds = YES;
    // 设置图片拉伸模式，让背景图能够跟随内容高度拉伸
    panel.contentMode = UIViewContentModeScaleToFill;
    [self.view addSubview:panel];
    self.panelView = panel;

    // 设置基本约束，底部约束将动态调整
    [NSLayoutConstraint activateConstraints:@[
        [panel.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor constant:14],
        [panel.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor constant:-14],
        [panel.topAnchor constraintEqualToAnchor:self.navView.bottomAnchor constant:20]
    ]];

    // 初始设置背景图底部约束到 Next 按钮上方 20pt（最大高度）
    self.panelBottomConstraint = [panel.bottomAnchor constraintEqualToAnchor:nextBtn.topAnchor constant:-20];
    self.panelBottomConstraint.active = YES;

    // 2. Step Logo 背景图（在背景图顶部）
    UIImageView *stepLogoBg = [[UIImageView alloc] initWithImage:[UIImage imageNamed:@"addressbook_info_step_header_bg"]];
    stepLogoBg.translatesAutoresizingMaskIntoConstraints = NO;
    stepLogoBg.contentMode = UIViewContentModeScaleToFill; // 跟随宽度拉伸
    stepLogoBg.userInteractionEnabled = YES;
    [panel addSubview:stepLogoBg];
    self.stepLogoBg = stepLogoBg;

    // Step Logo 背景图约束：宽度=背景图宽度，高度按比例自适应
    CGFloat stepHeaderRatio = 79.0 / 347.0; // 原始比例 79/347
    [NSLayoutConstraint activateConstraints:@[
        [stepLogoBg.leadingAnchor constraintEqualToAnchor:panel.leadingAnchor],
        [stepLogoBg.trailingAnchor constraintEqualToAnchor:panel.trailingAnchor],
        [stepLogoBg.topAnchor constraintEqualToAnchor:panel.topAnchor],
        [stepLogoBg.heightAnchor constraintEqualToAnchor:stepLogoBg.widthAnchor multiplier:stepHeaderRatio]
    ]];

    // 3. Step 标题区内容（数字 + 文案 + illustration）- 放在 stepLogoBg 上
    UILabel *stepNumLabel = [[UILabel alloc] init];
    stepNumLabel.text = @"4";
    stepNumLabel.font = [UIFont fontWithName:@"Verdana-BoldItalic" size:40];
    stepNumLabel.textColor = [UIColor colorWithRed:160/255.0 green:233/255.0 blue:234/255.0 alpha:1.0];
    stepNumLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [stepLogoBg addSubview:stepNumLabel];

    UILabel *stepTitleLabel = [[UILabel alloc] init];
    stepTitleLabel.text = @"Emergency\nContact Person"; // 按设计分两行
    stepTitleLabel.numberOfLines = 2;
    stepTitleLabel.font = [UIFont fontWithName:@"Verdana-BoldItalic" size:14];
    stepTitleLabel.textColor = UIColor.blackColor;
    stepTitleLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [stepLogoBg addSubview:stepTitleLabel];

    UIImageView *headerIllustration = [[UIImageView alloc] initWithImage:[UIImage imageNamed:@"AddBook"]];
    headerIllustration.translatesAutoresizingMaskIntoConstraints = NO;
    headerIllustration.contentMode = UIViewContentModeScaleAspectFit;
    [stepLogoBg addSubview:headerIllustration];

    [NSLayoutConstraint activateConstraints:@[
        [stepNumLabel.leadingAnchor constraintEqualToAnchor:stepLogoBg.leadingAnchor constant:14],
        [stepNumLabel.centerYAnchor constraintEqualToAnchor:stepLogoBg.centerYAnchor],
        [stepTitleLabel.leadingAnchor constraintEqualToAnchor:stepNumLabel.trailingAnchor constant:8],
        [stepTitleLabel.centerYAnchor constraintEqualToAnchor:stepLogoBg.centerYAnchor],
        [headerIllustration.trailingAnchor constraintEqualToAnchor:stepLogoBg.trailingAnchor constant:-14],
        [headerIllustration.centerYAnchor constraintEqualToAnchor:stepLogoBg.centerYAnchor],
        [headerIllustration.widthAnchor constraintEqualToConstant:113*SCALE_375],
        [headerIllustration.heightAnchor constraintEqualToConstant:70*SCALE_375]
    ]];

    // 3. 创建滚动视图容器，用于包含表单内容
    UIScrollView *scrollView = [[UIScrollView alloc] init];
    scrollView.translatesAutoresizingMaskIntoConstraints = NO;
    scrollView.backgroundColor = UIColor.clearColor;
    scrollView.showsVerticalScrollIndicator = YES;
    scrollView.showsHorizontalScrollIndicator = NO;
    scrollView.alwaysBounceVertical = NO; // 内容不足时不允许弹性滚动
    [self.view addSubview:scrollView];
    self.scrollView = scrollView;

    // 滚动视图约束：在背景图内部，从步骤logo背景图下方开始到背景图底部
    [NSLayoutConstraint activateConstraints:@[
        [scrollView.leadingAnchor constraintEqualToAnchor:panel.leadingAnchor constant:12],
        [scrollView.trailingAnchor constraintEqualToAnchor:panel.trailingAnchor constant:-12],
        [scrollView.topAnchor constraintEqualToAnchor:self.stepLogoBg.bottomAnchor], // 从步骤logo背景图下方开始
        [scrollView.bottomAnchor constraintEqualToAnchor:panel.bottomAnchor constant:-20] // 背景图底部上方20pt
    ]];

    // 4. TableView（放在滚动视图中，不限制高度）
    UITableViewStyle style = UITableViewStyleGrouped;
    self.tableView = [[UITableView alloc] initWithFrame:CGRectZero style:style];
    self.tableView.backgroundColor = UIColor.clearColor;
    self.tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    self.tableView.rowHeight = 55.0; // 含 5pt 间距
    self.tableView.dataSource = self;
    self.tableView.delegate = self;
    self.tableView.scrollEnabled = NO; // 禁用 tableView 自身滚动，由外层 scrollView 控制

    // iOS 15 以后默认会给 section header 顶部额外间距，手动去除
    if (@available(iOS 15.0, *)) {
        self.tableView.sectionHeaderTopPadding = 0.0;
    }

    // 对于Grouped样式，可能还有其他默认间距
    if (@available(iOS 11.0, *)) {
        self.tableView.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
    }
    self.tableView.translatesAutoresizingMaskIntoConstraints = NO;
    [scrollView addSubview:self.tableView];

    // tableView 在滚动视图中的约束
    [NSLayoutConstraint activateConstraints:@[
        [self.tableView.leadingAnchor constraintEqualToAnchor:scrollView.leadingAnchor constant:12],
        [self.tableView.trailingAnchor constraintEqualToAnchor:scrollView.trailingAnchor constant:-12],
        [self.tableView.topAnchor constraintEqualToAnchor:scrollView.topAnchor],
        [self.tableView.bottomAnchor constraintEqualToAnchor:scrollView.bottomAnchor],
        // 设置 tableView 宽度与滚动视图内容区域一致
        [self.tableView.widthAnchor constraintEqualToAnchor:scrollView.widthAnchor constant:-24] // 减去左右边距
    ]];
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    [self.navigationController setNavigationBarHidden:YES animated:animated];
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
    [self.navigationController setNavigationBarHidden:NO animated:animated];
}

- (void)viewDidLayoutSubviews {
    [super viewDidLayoutSubviews];
    // 移除静态变量，每次都检查是否需要更新高度
    if (self.templates.count > 0 && self.tableView.constraints.count > 0) {
        // 检查是否已经设置了高度约束
        BOOL hasHeightConstraint = NO;
        for (NSLayoutConstraint *constraint in self.tableView.constraints) {
            if (constraint.firstAttribute == NSLayoutAttributeHeight && constraint.firstItem == self.tableView) {
                hasHeightConstraint = YES;
                break;
            }
        }

        if (!hasHeightConstraint) {
            dispatch_async(dispatch_get_main_queue(), ^{
                [self updateTableViewHeightAfterDataLoad];
            });
        }
    }
}



#pragma mark - Networking

- (void)fetchContactsTemplate {
    MBProgressHUD *hud = [MBProgressHUD showHUDAddedTo:self.view animated:YES];
    hud.label.text = @"Loading…";

    NSDictionary *params = self.splendid ? @{ @"splendid": self.splendid } : @{};
    __weak typeof(self) weakSelf = self;
    [NetworkManager postFormWithAPI:@"Alicia/gotback" params:params completion:^(NSDictionary * _Nullable response, NSError * _Nullable error) {
        dispatch_async(dispatch_get_main_queue(), ^{
            [hud hideAnimated:YES];
            if (error) {
                NSLog(@"[AddressBook] Network error: %@", error);
                MBProgressHUD *err = [MBProgressHUD showHUDAddedTo:weakSelf.view animated:YES];
                err.mode = MBProgressHUDModeText;
                err.label.text = error.localizedDescription ?: @"Network error";
                [err hideAnimated:YES afterDelay:1.5];
                return;
            }

            // 添加详细的数据解析日志
            NSLog(@"[AddressBook] API Response: %@", response);

            // 更安全的数据解析
            NSDictionary *awkward = response[@"awkward"];
            if (![awkward isKindOfClass:[NSDictionary class]]) {
                NSLog(@"[AddressBook] Error: 'awkward' is not a dictionary. Response: %@", response);
                [weakSelf showErrorMessage:@"Data format error"];
                return;
            }

            NSArray *sober = awkward[@"sober"];
            if (![sober isKindOfClass:[NSArray class]]) {
                NSLog(@"[AddressBook] Error: 'sober' is not an array. Awkward: %@", awkward);
                [weakSelf showErrorMessage:@"Contact data format error"];
                return;
            }

            if (sober.count == 0) {
                NSLog(@"[AddressBook] Warning: Empty contact list");
                [weakSelf showErrorMessage:@"No contact templates available"];
                return;
            }

            NSLog(@"[AddressBook] Successfully parsed %lu contact templates", (unsigned long)sober.count);

            weakSelf.templates = [sober mutableCopy];
            weakSelf.outputs = [NSMutableArray arrayWithCapacity:sober.count];
            for (NSDictionary *tpl in sober) {
                NSMutableDictionary *d = [@{ @"name": tpl[@"excitedbecause"] ?: @"", @"phone": tpl[@"stools"] ?: @"", @"relationKey": tpl[@"thepool"] ?: @"", @"relationName": tpl[@"eyebrows"] ?: @"" } mutableCopy];
                [weakSelf.outputs addObject:d];
            }

            // 确保UI更新
            [weakSelf.tableView reloadData];
            [weakSelf.view setNeedsLayout];
            [weakSelf.view layoutIfNeeded];

            // 调试TableView状态
            [weakSelf debugTableViewState];

            // 延迟调用高度更新，确保数据已经加载完成
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [weakSelf updateTableViewHeightAfterDataLoad];
                // 再次调试状态
                [weakSelf debugTableViewState];
            });
        });
    }];
}

// 新增错误提示方法
- (void)showErrorMessage:(NSString *)message {
    MBProgressHUD *err = [MBProgressHUD showHUDAddedTo:self.view animated:YES];
    err.mode = MBProgressHUDModeText;
    err.label.text = message;
    [err hideAnimated:YES afterDelay:2.0];
}

// 新增调试方法：验证TableView状态
- (void)debugTableViewState {
    NSLog(@"[AddressBook] === TableView Debug Info ===");
    NSLog(@"[AddressBook] TableView frame: %@", NSStringFromCGRect(self.tableView.frame));
    NSLog(@"[AddressBook] TableView bounds: %@", NSStringFromCGRect(self.tableView.bounds));
    NSLog(@"[AddressBook] TableView superview: %@", self.tableView.superview);
    NSLog(@"[AddressBook] TableView hidden: %@", self.tableView.hidden ? @"YES" : @"NO");
    NSLog(@"[AddressBook] TableView alpha: %.2f", self.tableView.alpha);
    NSLog(@"[AddressBook] Templates count: %lu", (unsigned long)self.templates.count);
    NSLog(@"[AddressBook] Outputs count: %lu", (unsigned long)self.outputs.count);
    NSLog(@"[AddressBook] ScrollView frame: %@", NSStringFromCGRect(self.scrollView.frame));
    NSLog(@"[AddressBook] ScrollView contentSize: %@", NSStringFromCGSize(self.scrollView.contentSize));
    NSLog(@"[AddressBook] PanelView frame: %@", NSStringFromCGRect(self.panelView.frame));
    NSLog(@"[AddressBook] === End Debug Info ===");
}

// 新增测试数据方法：用于调试TableView显示问题
- (void)addTestDataForDebugging {
    NSLog(@"[AddressBook] Adding test data for debugging...");

    // 创建测试模板数据
    self.templates = [@[
        @{
            @"coldchicken": @"Emergency Contact 1",
            @"sing": @"Relationship",
            @"leaping": @"Select relationship",
            @"wentup": @"Phone Number",
            @"seldom": @"Select from contacts",
            @"excitedbecause": @"",
            @"stools": @"",
            @"thepool": @"",
            @"eyebrows": @"",
            @"takeanother": @[
                @{@"excitedbecause": @"Father", @"subject": @"father"},
                @{@"excitedbecause": @"Mother", @"subject": @"mother"},
                @{@"excitedbecause": @"Friend", @"subject": @"friend"}
            ]
        },
        @{
            @"coldchicken": @"Emergency Contact 2",
            @"sing": @"Relationship",
            @"leaping": @"Select relationship",
            @"wentup": @"Phone Number",
            @"seldom": @"Select from contacts",
            @"excitedbecause": @"",
            @"stools": @"",
            @"thepool": @"",
            @"eyebrows": @"",
            @"takeanother": @[
                @{@"excitedbecause": @"Father", @"subject": @"father"},
                @{@"excitedbecause": @"Mother", @"subject": @"mother"},
                @{@"excitedbecause": @"Friend", @"subject": @"friend"}
            ]
        }
    ] mutableCopy];

    // 创建对应的输出数据
    self.outputs = [NSMutableArray arrayWithCapacity:self.templates.count];
    for (NSDictionary *tpl in self.templates) {
        NSMutableDictionary *d = [@{
            @"name": @"",
            @"phone": @"",
            @"relationKey": @"",
            @"relationName": @""
        } mutableCopy];
        [self.outputs addObject:d];
    }

    NSLog(@"[AddressBook] Test data added: %lu templates, %lu outputs",
          (unsigned long)self.templates.count, (unsigned long)self.outputs.count);

    // 立即刷新TableView
    [self.tableView reloadData];
}

#pragma mark - Save

- (void)saveTapped {
    MBProgressHUD *hud = [MBProgressHUD showHUDAddedTo:self.view animated:YES];
    hud.label.text = @"Saving…";

    // 组装上传 JSON 数组
    NSMutableArray *uploadArr = [NSMutableArray arrayWithCapacity:self.outputs.count];
    for (NSDictionary *out in self.outputs) {
        NSMutableDictionary *dict = [NSMutableDictionary dictionary];
        if (out[@"name"]) dict[@"excitedbecause"] = out[@"name"]; // name
        if (out[@"relationKey"]) dict[@"thepool"] = out[@"relationKey"]; // relation enum key
        if (out[@"phone"]) dict[@"stools"] = out[@"phone"]; // phone number
        [uploadArr addObject:dict];
    }

    NSError *jsonErr = nil;
    NSData *jsonData = [NSJSONSerialization dataWithJSONObject:uploadArr options:0 error:&jsonErr];
    if (jsonErr) {
        [hud hideAnimated:YES];
        NSLog(@"[AddressBook] JSON error: %@", jsonErr);
        return;
    }

    NSString *jsonStr = [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];

    NSMutableDictionary *params = [@{ @"awkward": jsonStr } mutableCopy];
    if (self.splendid) params[@"splendid"] = self.splendid;

    __weak typeof(self) weakSelf = self;
    [NetworkManager postFormWithAPI:@"Alicia/chicken" params:params completion:^(NSDictionary * _Nullable response, NSError * _Nullable error) {
        dispatch_async(dispatch_get_main_queue(), ^{
            [hud hideAnimated:YES];
            NSString *message = nil;
            if (response && [response isKindOfClass:[NSDictionary class]]) {
                message = response[@"patted"] ?: @"<nil>";
                NSLog(@"[Save Contacts] patted: %@", message);
                
                NSNumber *modest = response[@"modest"];
                if ([modest respondsToSelector:@selector(integerValue)] && [modest integerValue] == 0) {
                    NSTimeInterval endTime = [[NSDate date] timeIntervalSince1970];
                    NSLog(@"📞 [埋点7-联系人] 开始上报 startTime:%.0f endTime:%.0f", weakSelf.pageStartTime, endTime);
                    [RiskEventManager reportEventType:RiskEventTypeContacts
                                           startTime:weakSelf.pageStartTime
                                             endTime:endTime
                                             orderId:nil];
                    
                    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                        [weakSelf.navigationController popViewControllerAnimated:YES];
                    });
                }
            } else if (error) {
                message = error.localizedDescription ?: @"Request error";
                NSLog(@"[Save Contacts] Request error: %@", message);
            }

            if (message.length > 0) {
                [HUD showToast:message inView:weakSelf.view];
            }
        });
    }];
}

#pragma mark - UITableViewDataSource

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    NSInteger count = self.templates.count;
    NSLog(@"[AddressBook] numberOfSectionsInTableView: %ld", (long)count);
    return count;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    NSLog(@"[AddressBook] numberOfRowsInSection %ld: returning 2", (long)section);
    return 2; // relation & phone
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    return 44.0; // 分组标题高度
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section {
    NSString *title = self.templates[section][@"coldchicken"] ?: @"";
    UIView *container = [[UIView alloc] init];
    container.backgroundColor = UIColor.clearColor;
    UILabel *lbl = [[UILabel alloc] init];
    lbl.translatesAutoresizingMaskIntoConstraints = NO;
    lbl.text = title;
    lbl.font = [UIFont boldSystemFontOfSize:16];
    lbl.textColor = UIColor.blackColor;
    [container addSubview:lbl];
    [NSLayoutConstraint activateConstraints:@[
        [lbl.leadingAnchor constraintEqualToAnchor:container.leadingAnchor constant:0],
        [lbl.bottomAnchor constraintEqualToAnchor:container.bottomAnchor constant:-8]
    ]];
    return container;
}

- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section { return 0.01; }

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    NSLog(@"[AddressBook] cellForRowAtIndexPath: section=%ld, row=%ld", (long)indexPath.section, (long)indexPath.row);

    static NSString *cid = @"ABFieldCell";
    UITableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:cid];
    if (!cell) {
        NSLog(@"[AddressBook] Creating new cell");
        cell = [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleValue1 reuseIdentifier:cid];
        cell.backgroundColor = UIColor.clearColor;
        // 移除默认边距，保证背景全宽
        cell.preservesSuperviewLayoutMargins = NO;
        cell.separatorInset = UIEdgeInsetsZero;
        cell.layoutMargins = UIEdgeInsetsZero;

        UIImageView *bg = [[UIImageView alloc] initWithImage:[UIImage imageNamed:@"personallnformation_cell_bg"]];
        bg.translatesAutoresizingMaskIntoConstraints = NO;
        [cell insertSubview:bg atIndex:0]; // 添加到 cell，覆盖 accessoryView 区域
        [NSLayoutConstraint activateConstraints:@[
            [bg.leadingAnchor constraintEqualToAnchor:cell.leadingAnchor],
            [bg.trailingAnchor constraintEqualToAnchor:cell.trailingAnchor],
            [bg.topAnchor constraintEqualToAnchor:cell.topAnchor],
            [bg.bottomAnchor constraintEqualToAnchor:cell.bottomAnchor constant:-5] // 底部留 5pt 间距
        ]];
        cell.detailTextLabel.textColor = UIColor.grayColor;
    }

    // 安全检查数组边界
    if (indexPath.section >= self.templates.count) {
        NSLog(@"[AddressBook] Error: section %ld >= templates.count %lu", (long)indexPath.section, (unsigned long)self.templates.count);
        return cell;
    }

    NSDictionary *tpl = self.templates[indexPath.section];
    NSMutableDictionary *out = self.outputs.count > indexPath.section ? self.outputs[indexPath.section] : nil;

    NSLog(@"[AddressBook] Template data: %@", tpl);
    NSLog(@"[AddressBook] Output data: %@", out);

    if (indexPath.row == 0) {
        // relation
        NSString *labelText = tpl[@"sing"] ?: @"Relation";
        NSString *detailText = out[@"relationName"] ?: tpl[@"leaping"] ?: @"";
        cell.textLabel.text = labelText;
        cell.detailTextLabel.text = detailText;
        cell.accessoryView = [[UIImageView alloc] initWithImage:[UIImage imageNamed:@"setting_cell_arrow"]];
        NSLog(@"[AddressBook] Relation cell - label: %@, detail: %@", labelText, detailText);
    } else {
        // phone
        NSString *labelText = tpl[@"wentup"] ?: @"Phone";
        NSString *name = out[@"name"] ?: @"";
        NSString *phone = out[@"phone"] ?: @"";
        NSString *detailText;
        if (name.length || phone.length) {
            detailText = [NSString stringWithFormat:@"%@ %@", name, phone];
        } else {
            detailText = tpl[@"seldom"] ?: @"";
        }
        cell.textLabel.text = labelText;
        cell.detailTextLabel.text = detailText;
        cell.accessoryView = [[UIImageView alloc] initWithImage:[UIImage imageNamed:@"AddBook_cell_bg"]];
        NSLog(@"[AddressBook] Phone cell - label: %@, detail: %@", labelText, detailText);
    }

    return cell;
}

#pragma mark - UITableViewDelegate

// didSelectRowAtIndexPath 保持原逻辑，仅调整 indexPath.section/row 使用
- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    // 先收起键盘，避免键盘遮挡选择器
    [self.view endEditing:YES];
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    NSDictionary *tpl = self.templates[indexPath.section];

    if (indexPath.row == 0) {
        // Relation picker（保持原实现）
        NSArray *options = tpl[@"takeanother"];
        if (options.count == 0) return;
        NSMutableArray<NSString *> *titles = [NSMutableArray array];
        NSMutableArray *keys = [NSMutableArray array];
        for (NSDictionary *opt in options) {
            NSString *t = opt[@"excitedbecause"] ?: @"";
            if (t.length) {
                [titles addObject:t];
                [keys addObject:opt[@"subject"] ?: @""];
            }
        }
        NSString *current = self.outputs[indexPath.section][@"relationName"];
        __weak typeof(self) weakSelf = self;
        OptionPickerViewController *picker = [[OptionPickerViewController alloc] initWithTitle:tpl[@"sing"]
                                                                                       options:titles
                                                                                selectedTitle:current
                                                                                     completion:^(NSInteger selectedIndex, NSString * _Nullable selectedTitle) {
            if (selectedIndex != NSNotFound && selectedIndex < keys.count) {
                NSMutableDictionary *out = weakSelf.outputs[indexPath.section];
                out[@"relationName"] = selectedTitle ?: @"";
                out[@"relationKey"]  = keys[selectedIndex];
                [weakSelf.tableView reloadSections:[NSIndexSet indexSetWithIndex:indexPath.section] withRowAnimation:UITableViewRowAnimationAutomatic];
            }
        }];
        [self presentViewController:picker animated:NO completion:nil];
    } else {
        // Phone picker（沿用原逻辑）
        CNAuthorizationStatus status = [CNContactStore authorizationStatusForEntityType:CNEntityTypeContacts];
        if (status == CNAuthorizationStatusDenied || status == CNAuthorizationStatusRestricted) {
            UIAlertController *alert = [UIAlertController alertControllerWithTitle:@"Contacts Permission Denied" message:@"Please enable contacts access in Settings" preferredStyle:UIAlertControllerStyleAlert];
            [alert addAction:[UIAlertAction actionWithTitle:@"OK" style:UIAlertActionStyleDefault handler:nil]];
            [self presentViewController:alert animated:YES completion:nil];
            return;
        }
        CNContactPickerViewController *picker = [CNContactPickerViewController new];
        picker.delegate = self;
        picker.predicateForEnablingContact = [NSPredicate predicateWithFormat:@"phoneNumbers.@count > 0"];
        picker.displayedPropertyKeys = @[CNContactGivenNameKey, CNContactFamilyNameKey, CNContactPhoneNumbersKey];
        picker.view.tag = indexPath.section;
        [self presentViewController:picker animated:NO completion:nil];
    }
}

// 固定行高 50
- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    return 55.0; // 实际可视高度 50 + 底部间距 5
}

#pragma mark - CNContactPickerDelegate

- (void)contactPicker:(CNContactPickerViewController *)picker didSelectContactProperty:(CNContactProperty *)contactProperty {
    NSInteger section = picker.view.tag;
    if (section < 0 || section >= self.outputs.count) return;

    CNContact *contact = contactProperty.contact;
    CNPhoneNumber *phoneValue = (CNPhoneNumber *)contactProperty.value;
    NSString *phone = phoneValue.stringValue ?: @"";

    NSString *fullName = [AddressBookAuthenticationViewController ab_safeFullNameForContact:contact];

    NSMutableDictionary *out = self.outputs[section];
    out[@"name"]  = fullName;
    out[@"phone"] = phone;

    NSLog(@"[AddressBook] Selected contact: %@ %@", fullName, phone);

    [self.tableView reloadSections:[NSIndexSet indexSetWithIndex:section] withRowAnimation:UITableViewRowAnimationAutomatic];

    // 在后台线程打印全量联系人，避免主线程阻塞
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        [self logAllContacts];
    });
}

#pragma mark - CNContactPickerDelegate (Quick select)

- (void)contactPicker:(CNContactPickerViewController *)picker didSelectContact:(CNContact *)contact {
    // 由于已设置 displayedPropertyKeys ，此处可直接使用 contact.phoneNumbers
    if (contact.phoneNumbers.count == 0) return;

    CNPhoneNumber *num = contact.phoneNumbers.firstObject.value;
    NSString *phone = num.stringValue ?: @"";
    NSString *fullName = [AddressBookAuthenticationViewController ab_safeFullNameForContact:contact];

    NSInteger section = picker.view.tag;
    if (section < 0 || section >= self.outputs.count) return;

    NSMutableDictionary *out = self.outputs[section];
    out[@"name"]  = fullName;
    out[@"phone"] = phone;

    NSLog(@"[AddressBook] Selected contact: %@ %@", fullName, phone);

    [self.tableView reloadSections:[NSIndexSet indexSetWithIndex:section] withRowAnimation:UITableViewRowAnimationAutomatic];

    // 在后台线程打印全量联系人，避免主线程阻塞
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        [self logAllContacts];
    });
}

#pragma mark - Helpers

- (void)logAllContacts {
    CNContactStore *store = [CNContactStore new];
    NSArray *keys = @[CNContactGivenNameKey, CNContactFamilyNameKey, CNContactMiddleNameKey, CNContactPhoneNumbersKey];
    CNContactFetchRequest *request = [[CNContactFetchRequest alloc] initWithKeysToFetch:keys];

    NSMutableArray *arr = [NSMutableArray array];
    NSError *err = nil;
    [store enumerateContactsWithFetchRequest:request error:&err usingBlock:^(CNContact * _Nonnull contact, BOOL * _Nonnull stop) {
        NSString *name = [AddressBookAuthenticationViewController ab_safeFullNameForContact:contact];
        for (CNLabeledValue *lbl in contact.phoneNumbers) {
            CNPhoneNumber *num = lbl.value;
            NSString *one = [NSString stringWithFormat:@"%@ %@", name, num.stringValue ?: @""];
            [arr addObject:one];
        }
    }];

    if (err) {
        NSLog(@"[AddressBook] Fetch contacts error: %@", err);
    } else {
        NSLog(@"[AddressBook] All contacts: %@", arr);
    }
}

#pragma mark - Properties Synthesized

@synthesize tableView = _tableView;
@synthesize templates = _templates;
@synthesize outputs = _outputs;

#pragma mark - Private Methods

// 新增方法：设置背景图
- (void)setupBackgroundImage {
    UIImageView *bgView = [[UIImageView alloc] initWithFrame:self.view.bounds];
    bgView.image = [UIImage imageNamed:@"general_background"];
    bgView.contentMode = UIViewContentModeScaleAspectFill;
    bgView.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;
    bgView.userInteractionEnabled = NO;
    bgView.alpha = 1.0; // 可根据需要调整透明度
    [self.view insertSubview:bgView atIndex:0];
}

#pragma mark - Keyboard

- (void)dismissKeyboard {
    [self.view endEditing:YES];
}

#pragma mark - Dynamic Height Update

// 重命名并改进原有的方法
- (void)updateTableViewHeightAfterDataLoad {
    NSLog(@"[AddressBook] Starting table view height update...");

    // 确保数据已加载
    if (self.templates.count == 0) {
        NSLog(@"[AddressBook] No templates data, skipping height update");
        return;
    }

    // 强制 tableView 重新计算内容大小
    [self.tableView reloadData];
    [self.tableView layoutIfNeeded];

    NSInteger sectionCount = [self.tableView numberOfSections];
    NSLog(@"[AddressBook] Section count: %ld", (long)sectionCount);

    // 计算 tableView 实际需要的高度
    CGFloat totalHeight = 0;
    for (NSInteger section = 0; section < sectionCount; section++) {
        CGFloat headerHeight = [self tableView:self.tableView heightForHeaderInSection:section];
        CGFloat footerHeight = [self tableView:self.tableView heightForFooterInSection:section];
        totalHeight += headerHeight + footerHeight;

        NSInteger rowCount = [self.tableView numberOfRowsInSection:section];
        for (NSInteger row = 0; row < rowCount; row++) {
            NSIndexPath *indexPath = [NSIndexPath indexPathForRow:row inSection:section];
            CGFloat rowHeight = [self tableView:self.tableView heightForRowAtIndexPath:indexPath];
            totalHeight += rowHeight;
        }
    }

    // 更新 tableView 的高度约束
    // 移除之前的高度约束（如果存在）
    NSMutableArray *constraintsToRemove = [NSMutableArray array];
    for (NSLayoutConstraint *constraint in self.tableView.constraints) {
        if (constraint.firstAttribute == NSLayoutAttributeHeight && constraint.firstItem == self.tableView) {
            [constraintsToRemove addObject:constraint];
        }
    }
    [NSLayoutConstraint deactivateConstraints:constraintsToRemove];

    // 让tableView自己计算需要的高度
    CGSize fittingSize = [self.tableView sizeThatFits:CGSizeMake(self.tableView.frame.size.width, CGFLOAT_MAX)];
    CGFloat contentSizeHeight = self.tableView.contentSize.height;

    // 使用最大值并添加适当的缓冲确保完整显示
    CGFloat actualHeight = MAX(MAX(totalHeight, contentSizeHeight), fittingSize.height) + 30;

    NSLog(@"[AddressBook] 高度计算: 手动=%.1f, contentSize=%.1f, sizeThatFits=%.1f, 最终=%.1f",
          totalHeight, contentSizeHeight, fittingSize.height, actualHeight);

    // 添加新的高度约束
    NSLayoutConstraint *heightConstraint = [self.tableView.heightAnchor constraintEqualToConstant:actualHeight];
    heightConstraint.active = YES;

    // 计算背景图需要的高度
    [self updatePanelHeight:actualHeight];

    // 更新布局
    [self.view setNeedsLayout];
    [self.view layoutIfNeeded];

    NSLog(@"[AddressBook] Table view height update completed");
}

- (void)updatePanelHeight:(CGFloat)tableViewHeight {
    // 计算步骤logo背景图的动态高度
    CGFloat panelWidth = self.view.bounds.size.width - 28; // 减去左右边距14*2
    CGFloat stepHeaderRatio = 79.0 / 347.0;
    CGFloat headerHeight = panelWidth * stepHeaderRatio; // 动态计算标题区高度

    // scrollView已经有20pt底部间距，tableView在scrollView内部还需要一些内边距
    CGFloat scrollViewBottomPadding = 20; // scrollView到背景图底部的间距（已在约束中设置）
    CGFloat tableViewInternalPadding = 12; // tableView内部可能需要的额外间距
    CGFloat requiredPanelHeight = headerHeight + tableViewHeight + scrollViewBottomPadding + tableViewInternalPadding;

    // 计算背景图的最大允许高度（到Next按钮上方20pt）
    CGFloat maxPanelHeight = self.nextButton.frame.origin.y - (self.navView.frame.origin.y + self.navView.frame.size.height + 20) - 20;

    // 如果还没有布局完成，使用约束计算最大高度
    if (maxPanelHeight <= 0) {
        // 使用视图高度和安全区域计算
        CGFloat safeAreaBottom = self.view.safeAreaInsets.bottom;
        CGFloat nextButtonHeight = 50;
        CGFloat nextButtonBottomMargin = 20;
        CGFloat panelTopMargin = 20;
        CGFloat navHeight = 44;

        maxPanelHeight = self.view.bounds.size.height - safeAreaBottom - nextButtonHeight - nextButtonBottomMargin - panelTopMargin - navHeight - self.view.safeAreaInsets.top - 20;
    }



    // 更新背景图的底部约束
    self.panelBottomConstraint.active = NO;

    NSLog(@"[AddressBook] 背景图高度计算: header=%.1f + table=%.1f + padding=%.1f = %.1f, max=%.1f",
          headerHeight, tableViewHeight, scrollViewBottomPadding + tableViewInternalPadding, requiredPanelHeight, maxPanelHeight);

    if (requiredPanelHeight <= maxPanelHeight) {
        // 表单内容较少，背景图高度刚好包含内容
        self.panelBottomConstraint = [self.panelView.heightAnchor constraintEqualToConstant:requiredPanelHeight];
        // 禁用滚动，因为内容都能显示
        self.scrollView.scrollEnabled = NO;

    } else {
        // 表单内容较多，背景图使用最大高度，启用滚动
        self.panelBottomConstraint = [self.panelView.bottomAnchor constraintEqualToAnchor:self.nextButton.topAnchor constant:-20];
        self.scrollView.scrollEnabled = YES;

    }

    self.panelBottomConstraint.active = YES;
}

@end
